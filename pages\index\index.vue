<template>
  <!-- 导航类型选择头部 -->
  <GaodeHeader
    class="noDaohang"
    @changeType="handleNavigationTypeChange"
    :type="gaode_type"
    :NavigationOrNot="daohang"
    :buttonsDisabled="buttonsDisabled"
  />

  <!-- 搜索输入区域 -->
  <view class="section">
    <!-- 起点输入框 -->
    <GaodeInputTips
      @customEvent="handleLocationSelect"
      :city="city"
      :longitude="longitude"
      :latitude="latitude"
      :defaultValue="'当前位置'"
      inputType="start"
    />

    <!-- 终点输入框 -->
    <GaodeInputTips
      @customEvent="handleLocationSelect"
      :city="city_e"
      :longitude="longitude_e"
      :latitude="latitude_e"
      :defaultValue="'目的地'"
      inputType="end"
    />


  </view>

  <!-- 地图容器 -->
  <view class="map_container">
    <!-- 地图组件 - 非公交模式时显示 -->
    <map
      v-if="gaode_type !== 'bus' && mapState"
      class="map"
      id="map"
      :longitude="longitude"
      :latitude="latitude"
      scale="14"
      show-location="true"
      :markers="markers"
      @markertap="handleMarkerTap"
      :polyline="polyline"
      :include-points="includePoints"
    />

    <!-- 公交路线列表 - 公交模式时显示 -->
    <view v-if="gaode_type === 'bus' && transits.length > 0" class="transit_container">
      <view class="transit-header-title">
        <text class="title-text">公交路线方案</text>
        <text class="count-text">{{ transits.length }}个方案</text>
        <text v-if="isInterCityRoute" class="intercity-tip">跨城市路线</text>
      </view>

      <view class="transit-list">
        <view
          v-for="(transit, index) in transits"
          :key="index"
          class="transit-item"
          @click="selectTransit(transit, index)"
        >
          <!-- 路线头部 -->
          <view class="route-header">
            <view class="time-info">
              <text class="duration">{{ Math.round(transit.duration / 60) }}分钟</text>
              <text class="arrival">{{ getArrivalTime(transit.duration) }}到达</text>
            </view>
            <view class="tags">
              <text v-if="index === 0" class="recommend-tag">推荐</text>
              <text v-if="transit.nightflag === '1'" class="night-tag">夜班</text>
            </view>
          </view>

          <!-- 路线展示 -->
          <view class="route-display">
            <view class="route-line">
              <view class="start-point"></view>
              <view class="route-segments-container">
                <view
                  v-for="(segment, segIndex) in getSimpleSegments(transit)"
                  :key="segIndex"
                  class="segment-with-connector"
                >
                  <view v-if="segIndex > 0" class="connector-line"></view>
                  <view class="route-segment">
                    <view v-if="segment.type === 'walking'" class="walking-segment">
                      <text class="walking-text">步行{{ segment.distance }}米</text>
                    </view>
                    <view v-else-if="segment.type === 'bus'" class="bus-segment">
                      <text class="bus-name">{{ segment.busline }}</text>
                      <text class="station-count">{{ segment.stationCount }}站</text>
                    </view>
                  </view>
                </view>
              </view>
              <view class="end-point"></view>
            </view>
          </view>

          <!-- 路线信息 -->
          <view class="route-info">
            <view class="info-item">
              <text class="info-label">距离</text>
              <text class="info-value">{{ (transit.distance / 1000).toFixed(1) }}公里</text>
            </view>
            <view class="info-item">
              <text class="info-label">费用</text>
              <text class="info-value">{{ transit.cost }}元</text>
            </view>
            <view class="info-item">
              <text class="info-label">步行</text>
              <text class="info-value">{{ Math.round(transit.walking_distance) }}米</text>
            </view>
          </view>

          <!-- 展开详情 -->
          <view class="expand-btn" @click.stop="toggleDetails(index)">
            <text class="expand-text">{{ expandedIndex === index ? '收起详情' : '查看详情' }}</text>
            <text class="expand-icon">{{ expandedIndex === index ? '▲' : '▼' }}</text>
          </view>

          <!-- 详细信息 -->
          <view v-if="expandedIndex === index" class="route-details">
            <view
              v-for="(segment, segIndex) in transit.segments"
              :key="segIndex"
              class="detail-item"
            >
              <!-- 步行详情 -->
              <view v-if="segment.walking?.distance" class="walking-detail">
                <view class="detail-header">
                  <text class="detail-icon">🚶</text>
                  <text class="detail-title">步行 {{ segment.walking.distance }}米</text>
                  <text class="detail-time">约{{ Math.round(segment.walking.duration / 60) }}分钟</text>
                </view>
                <view class="walking-steps">
                  <text
                    v-for="(step, stepIndex) in segment.walking.steps"
                    :key="stepIndex"
                    class="step-text"
                  >
                    {{ step.instruction }}
                  </text>
                </view>
              </view>

              <!-- 公交详情 -->
              <view v-if="segment.bus?.buslines?.[0]" class="bus-detail">
                <view class="detail-header">
                  <text class="detail-icon">🚌</text>
                  <text class="detail-title">{{ segment.bus.buslines[0].name }}</text>
                  <text class="detail-time">{{ Math.round(segment.bus.buslines[0].duration / 60) }}分钟</text>
                </view>

                <view class="bus-stations">
                  <view class="station-row">
                    <text class="station-label">上车：</text>
                    <text class="station-name">{{ segment.bus.buslines[0].departure_stop.name }}</text>
                  </view>
                  <view class="station-row">
                    <text class="station-label">下车：</text>
                    <text class="station-name">{{ segment.bus.buslines[0].arrival_stop.name }}</text>
                  </view>
                  <view class="station-row">
                    <text class="station-label">运营：</text>
                    <text class="station-name">{{ segment.bus.buslines[0].start_time }}-{{ segment.bus.buslines[0].end_time }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部信息显示区域 -->
  <view class="map_text" v-if="gaode_type !== 'bus'">
    <view class="text_info_horizontal">
      <text class="h1">{{ textData.name }}</text>
      <text class="desc_text">{{ textData.desc }}</text>
    </view>

    <!-- 导航信息 - 有路线时显示 -->
    <view class="text_box" v-if="daohang">
      <view class="text">{{ distance }}</view>
      <view class="text">{{ cost }}</view>
    </view>
  </view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref, watchEffect } from 'vue'

// 导入自定义组件
import GaodeHeader from '../../components/Header/Header.vue'
import GaodeInputTips from '../../components/InputTip/InputTip.vue'

// 导入组合式函数
import { useMapState } from '../../composables/useMapState.js'
import { useRouteService } from '../../composables/useRouteService.js'
import { useLocationService } from '../../composables/useLocationService.js'
import { useRealTimeLocation } from '../../composables/useRealTimeLocation.js'

// 导入工具函数
import { safeFilterMarkers } from '../../utils/mapUtils.js'

// 导入高德地图SDK
import AMapWX from '../../libs/amap-wx.130.js'
import gaode_key from '../../libs/config.js'

// ===== 组合式函数初始化 =====

// 地图状态管理
const {
  mapState, markers, polyline, includePoints,
  latitude, longitude, city,
  latitude_e, longitude_e, city_e, mapEndObj,
  textData, distance, cost, daohang, gaode_type, transits,
  currentLocation, destinationLocation, hasValidRoute,
  setCurrentLocation, setDestination, setRouteMarkers,
  showMarkerInfo, changeMarkerColor, resetMapState, changeNavigationType
} = useMapState()

// 路线服务
const { planRoute } = useRouteService()

// 位置服务
const { getPoiAround } = useLocationService()

// 实时定位服务
const { isTracking, currentPosition, hasValidPosition, startTracking } = useRealTimeLocation()

// ===== 计算属性 =====

// 判断功能按钮是否应该被禁用
const buttonsDisabled = computed(() => {
  const hasStartPoint = !!(longitude.value && latitude.value)
  const hasEndPoint = !!(longitude_e.value && latitude_e.value)
  return !(hasStartPoint && hasEndPoint)
})

// 检测是否为跨城市路线
const isInterCityRoute = computed(() => {
  if (!city.value || !city_e.value) return false
  const normalizeCity = (cityName) => cityName?.replace(/[市区县]/g, '').trim() || ''
  return normalizeCity(city.value) !== normalizeCity(city_e.value)
})

// 全局变量 - 保存原始标记数据
let markersData = []

// 响应式数据
const expandedIndex = ref(-1)

// 页面加载完成事件
onLoad(async () => {
  initializeCurrentLocation()
  startTracking().catch(() => {}) // 静默处理定位失败
})

// 实时定位监听
watchEffect(() => {
  if (isTracking.value && hasValidPosition.value && !daohang.value) {
    const pos = currentPosition.value
    longitude.value = pos.longitude
    latitude.value = pos.latitude
    updateCurrentLocationMarker(pos)
  }
})

// 更新当前位置标记
function updateCurrentLocationMarker(position) {
  const currentMarkerIndex = markersData?.findIndex(marker => marker.id === 999)
  if (currentMarkerIndex > -1) {
    markersData[currentMarkerIndex] = {
      ...markersData[currentMarkerIndex],
      latitude: position.latitude,
      longitude: position.longitude
    }
    markers.value = [...markersData]
  }
}

// 地图标记点击事件处理
function handleMarkerTap(e) {
  if (markersData?.length > 0) {
    showMarkerInfo(markersData, e.markerId)
    changeMarkerColor(markersData, e.markerId)
  }
}

// 导航类型切换事件处理
function handleNavigationTypeChange(data) {
  changeNavigationType(data.gaode_type)
  if (hasValidRoute.value) planRouteWithCurrentType()
}

// 位置选择事件处理
function handleLocationSelect(data) {
  const { info, inputType } = data

  if (info === null) {
    inputType === 'start' ? handleClearStartLocation() : handleClearEndLocation()
    return
  }

  inputType === 'start' ? handleStartLocationSelect(info) : handleEndLocationSelect(info)
}

// 业务逻辑函数

// 初始化当前位置
function initializeCurrentLocation() {
  const iconConfig = {
    iconPath: '/static/marker.png',
    iconPathSelected: '/static/marker_checked.png'
  }

  getPoiAround(iconConfig, handlePoiSuccess, tryGetDeviceLocation)
}

// 处理POI获取成功
function handlePoiSuccess(data) {
  if (!data?.markers?.length) {
    uni.showToast({ title: '无法获取当前位置', icon: 'none' })
    return
  }

  // 格式化POI标记点数据
  const formattedPOIMarkers = data.markers.map((marker, index) => ({
    id: marker.id ?? index,
    latitude: parseFloat(marker.latitude),
    longitude: parseFloat(marker.longitude),
    iconPath: '/static/marker.png',
    width: 23,
    height: 33,
    name: marker.name || `POI-${index}`,
    address: marker.address || '未知地址',
    callout: {
      content: marker.name || `POI-${index}`,
      display: 'BYCLICK',
      fontSize: 12,
      borderRadius: 5,
      bgColor: '#ffffff',
      padding: 5
    }
  }))

  markersData = formattedPOIMarkers

  // 设置地图中心和当前位置标记
  if (data.currentLocation?.location) {
    const [lng, lat] = data.currentLocation.location.split(',')
    if (lng && lat) {
      longitude.value = parseFloat(lng)
      latitude.value = parseFloat(lat)
      city.value = data.currentLocation.name || ''

      const currentLocationMarker = {
        id: 999,
        latitude: parseFloat(lat),
        longitude: parseFloat(lng),
        iconPath: '/static/marker_checked.png',
        width: 23,
        height: 33,
        name: data.currentLocation.name || '当前位置',
        address: data.currentLocation.district || data.currentLocation.address || '',
        callout: {
          content: data.currentLocation.name || '当前位置',
          display: 'ALWAYS',
          fontSize: 14,
          borderRadius: 5,
          bgColor: '#ff0000',
          color: '#ffffff',
          padding: 8
        }
      }

      markers.value = [currentLocationMarker, ...formattedPOIMarkers]
    }
  } else {
    markers.value = formattedPOIMarkers
    const firstMarker = formattedPOIMarkers[0]
    if (firstMarker) {
      longitude.value = firstMarker.longitude
      latitude.value = firstMarker.latitude
      city.value = firstMarker.name
    }
  }

  // 设置包含点
  includePoints.value = markers.value
    .filter(marker => marker?.latitude && marker?.longitude)
    .map(marker => ({ latitude: marker.latitude, longitude: marker.longitude }))
}

// 备用方案：尝试直接获取设备位置
function tryGetDeviceLocation() {
  uni.getLocation({
    type: 'gcj02',
    success: (res) => {
      longitude.value = res.longitude
      latitude.value = res.latitude
      city.value = '当前位置'

      const deviceMarker = {
        iconPath: '/static/marker_checked.png',
        id: 999,
        latitude: res.latitude,
        longitude: res.longitude,
        width: 23,
        height: 33,
        name: '当前位置',
        address: '设备定位',
        callout: {
          content: '当前位置',
          display: 'ALWAYS',
          fontSize: 14,
          borderRadius: 5,
          bgColor: '#ff0000',
          color: '#ffffff',
          padding: 8
        }
      }

      const poiMarkers = markersData?.filter(marker => marker.id !== 999) || []
      markersData = [deviceMarker, ...poiMarkers]
      markers.value = markersData

      uni.showToast({ title: '已获取设备位置', icon: 'success' })
    },
    fail: useDefaultLocation
  })
}

// 最后的备用方案：使用默认位置
function useDefaultLocation() {
  const defaultLocation = { name: '天安门', address: '北京市东城区东长安街', latitude: 39.90923, longitude: 116.397428 }

  longitude.value = defaultLocation.longitude
  latitude.value = defaultLocation.latitude
  city.value = defaultLocation.name

  const currentLocationMarker = {
    iconPath: '/static/marker_checked.png',
    id: 999,
    latitude: defaultLocation.latitude,
    longitude: defaultLocation.longitude,
    width: 23,
    height: 33,
    name: defaultLocation.name,
    address: defaultLocation.address,
    callout: {
      content: defaultLocation.name,
      display: 'ALWAYS',
      fontSize: 14,
      borderRadius: 5,
      bgColor: '#ff0000',
      color: '#ffffff',
      padding: 8
    }
  }

  const testPOIMarkers = [
    { iconPath: '/static/marker.png', id: 1, latitude: 39.91023, longitude: 116.398428, width: 23, height: 33, name: '测试POI1', address: '测试地址1', callout: { content: '测试POI1', display: 'BYCLICK', fontSize: 12, borderRadius: 5, bgColor: '#ffffff', padding: 5 } },
    { iconPath: '/static/marker.png', id: 2, latitude: 39.90823, longitude: 116.396428, width: 23, height: 33, name: '测试POI2', address: '测试地址2', callout: { content: '测试POI2', display: 'BYCLICK', fontSize: 12, borderRadius: 5, bgColor: '#ffffff', padding: 5 } }
  ]

  markersData = [currentLocationMarker, ...testPOIMarkers]
  markers.value = markersData

  uni.showToast({ title: '已设置默认位置', icon: 'none' })
}

// 处理起点位置选择
function handleStartLocationSelect(locationInfo) {
  if (!locationInfo) {
    uni.showToast({ title: '位置信息无效', icon: 'none' })
    return
  }

  // 保存终点数据
  const savedEndData = {
    latitude_e: latitude_e.value,
    longitude_e: longitude_e.value,
    city_e: city_e.value,
    mapEndObj: { ...mapEndObj.value }
  }

  resetMapState()
  setCurrentLocation(locationInfo)

  // 恢复终点数据
  if (savedEndData.latitude_e && savedEndData.longitude_e) {
    latitude_e.value = savedEndData.latitude_e
    longitude_e.value = savedEndData.longitude_e
    city_e.value = savedEndData.city_e
    mapEndObj.value = savedEndData.mapEndObj
    daohang.value = true
  }

  // 创建起点标记
  if (locationInfo.location) {
    const [lng, lat] = locationInfo.location.split(',')
    if (lng && lat) {
      const startMarker = {
        iconPath: '/static/marker_checked.png',
        id: 0,
        latitude: parseFloat(lat),
        longitude: parseFloat(lng),
        width: 23,
        height: 33,
        name: locationInfo.name || '起点',
        address: locationInfo.district || locationInfo.address || ''
      }
      markersData = [startMarker]
      markers.value = [startMarker]
    }
  }

  if (hasValidRoute.value) planRouteWithCurrentType()
}

// 处理终点位置选择
function handleEndLocationSelect(locationInfo) {
  if (!locationInfo) {
    uni.showToast({ title: '位置信息无效', icon: 'none' })
    return
  }

  setDestination(locationInfo)
  if (hasValidRoute.value) planRouteWithCurrentType()
}

// 清空起点位置
function handleClearStartLocation() {
  // 保存终点数据
  const savedEndData = {
    latitude_e: latitude_e.value,
    longitude_e: longitude_e.value,
    city_e: city_e.value,
    mapEndObj: { ...mapEndObj.value }
  }

  // 清空起点数据和状态
  longitude.value = ''
  latitude.value = ''
  city.value = ''
  daohang.value = false
  markers.value = []
  polyline.value = []
  includePoints.value = []
  textData.value = {}
  distance.value = ''
  cost.value = ''
  mapState.value = true
  transits.value = []

  // 恢复终点数据
  if (savedEndData.latitude_e && savedEndData.longitude_e) {
    latitude_e.value = savedEndData.latitude_e
    longitude_e.value = savedEndData.longitude_e
    city_e.value = savedEndData.city_e
    mapEndObj.value = savedEndData.mapEndObj
  }

  // 延迟获取POI数据
  setTimeout(() => {
    if (!longitude.value && !latitude.value) {
      const iconConfig = { iconPath: '/static/marker.png', iconPathSelected: '/static/marker_checked.png' }
      getPoiAround(iconConfig, (data) => {
        if (data?.markers?.length > 0) {
          const validMarkers = safeFilterMarkers(data.markers)
          const formattedPOIMarkers = validMarkers.map((marker, index) => ({
            id: marker.id ?? index,
            latitude: parseFloat(marker.latitude),
            longitude: parseFloat(marker.longitude),
            iconPath: '/static/marker.png',
            width: 23,
            height: 33,
            name: marker.name || `POI-${index}`,
            address: marker.address || '未知地址',
            callout: { content: marker.name || `POI-${index}`, display: 'BYCLICK', fontSize: 12, borderRadius: 5, bgColor: '#ffffff', padding: 5 }
          }))
          markersData = formattedPOIMarkers
          markers.value = formattedPOIMarkers
        }
      }, () => { markers.value = []; markersData = [] })
    }
  }, 100)
}

// 清空终点位置
function handleClearEndLocation() {
  const savedStartData = { latitude: latitude.value, longitude: longitude.value, city: city.value }

  // 清空终点数据和状态
  longitude_e.value = ''
  latitude_e.value = ''
  city_e.value = ''
  mapEndObj.value = {}
  daohang.value = false
  polyline.value = []
  distance.value = ''
  cost.value = ''
  transits.value = []
  textData.value = {}

  // 恢复起点数据
  if (savedStartData.latitude && savedStartData.longitude) {
    latitude.value = savedStartData.latitude
    longitude.value = savedStartData.longitude
    city.value = savedStartData.city

    // 延迟恢复POI浏览模式
    setTimeout(() => {
      if (latitude.value && longitude.value) {
        const iconConfig = { iconPath: '/static/marker.png', iconPathSelected: '/static/marker_checked.png' }
        getPoiAround(iconConfig, (data) => {
          if (data?.markers?.length > 0) {
            const formattedPOIMarkers = data.markers
              .filter(marker => marker && typeof marker.latitude === 'number' && typeof marker.longitude === 'number' && !isNaN(marker.latitude) && !isNaN(marker.longitude) && !(marker.latitude === 0 && marker.longitude === 0))
              .map((marker, index) => ({
                id: marker.id ?? index,
                latitude: parseFloat(marker.latitude),
                longitude: parseFloat(marker.longitude),
                iconPath: '/static/marker.png',
                width: 23,
                height: 33,
                name: marker.name || `POI-${index}`,
                address: marker.address || '未知地址',
                callout: { content: marker.name || `POI-${index}`, display: 'BYCLICK', fontSize: 12, borderRadius: 5, bgColor: '#ffffff', padding: 5 }
              }))

            const currentLocationMarker = {
              id: 999,
              latitude: savedStartData.latitude,
              longitude: savedStartData.longitude,
              iconPath: '/static/marker_checked.png',
              width: 23,
              height: 33,
              name: savedStartData.city || '当前位置',
              address: '起点位置',
              callout: { content: savedStartData.city || '当前位置', display: 'ALWAYS', fontSize: 14, borderRadius: 5, bgColor: '#ff0000', color: '#ffffff', padding: 8 }
            }

            markersData = [currentLocationMarker, ...formattedPOIMarkers]
            markers.value = markersData
          }
        }, () => {
          const currentLocationMarker = { id: 999, latitude: savedStartData.latitude, longitude: savedStartData.longitude, iconPath: '/static/marker_checked.png', width: 23, height: 33, name: savedStartData.city || '当前位置', address: '起点位置' }
          markers.value = [currentLocationMarker]
          markersData = [currentLocationMarker]
        })
      }
    }, 100)
  }
}

// 使用当前导航类型规划路线
function planRouteWithCurrentType() {
  if (!hasValidRoute.value) return

  const routeParams = {
    origin: currentLocation.value,
    destination: destinationLocation.value,
    city: city.value || '广州',
    cityd: city_e.value
  }

  setRouteMarkers()
  planRoute(gaode_type.value, routeParams, handleRouteSuccess, handleRouteError)
}

// 路线规划成功回调
function handleRouteSuccess(routeData) {
  if (gaode_type.value === 'bus') {
    if (routeData.transits) {
      transits.value = routeData.transits
      mapState.value = false
    }
  } else {
    if (routeData.polyline) polyline.value = routeData.polyline
    if (routeData.distance) distance.value = routeData.distance
    if (routeData.cost) cost.value = routeData.cost
    mapState.value = true
  }
}

// 路线规划失败回调
function handleRouteError(error) {
  let errorMessage = '路线规划失败，请重试'

  if (error?.message) {
    errorMessage = error.message

    // 特殊处理步行建议
    if (error.showWalkingSuggestion) {
      const walkingTime = error.walkingTime || 0
      const transitTime = error.transitTime || 0

      uni.showToast({
        title: `步行更便利！步行${walkingTime}分钟，公交${transitTime}分钟`,
        icon: 'none',
        duration: 3000
      })

      setTimeout(() => {
        uni.showModal({
          title: '出行建议',
          content: `步行更加便利！\n\n步行时间：约${walkingTime}分钟\n公交时间：约${transitTime}分钟\n\n建议您选择步行出行`,
          showCancel: true,
          cancelText: '继续查看公交',
          confirmText: '切换步行',
          success: (res) => {
            if (res.confirm) {
              gaode_type.value = 'walk'
              planRoute()
            } else if (res.cancel) {
              forceShowTransitRoute()
            }
          }
        })
      }, 500)
      return
    }
  } else if (gaode_type.value === 'bus') {
    const isInterCity = city.value && city_e.value && city.value !== city_e.value
    errorMessage = isInterCity
      ? `暂无从${city.value}到${city_e.value}的公交路线，建议选择驾车或火车出行`
      : '未找到合适的公交路线，请尝试其他出行方式'
  }

  uni.showToast({ title: errorMessage, icon: 'none', duration: 3000 })
}

// 强制显示公交路线（忽略步行时间比较）
function forceShowTransitRoute() {
  const params = {
    origin: `${longitude.value},${latitude.value}`,
    destination: `${longitude_e.value},${latitude_e.value}`,
    city: city.value,
    cityd: city_e.value
  }

  const myAmapFun = new AMapWX.AMapWX({ key: gaode_key.key })
  const requestParams = { origin: params.origin, destination: params.destination, city: params.city }

  if (params.cityd && params.cityd !== params.city) {
    Object.assign(requestParams, { cityd: params.cityd })
  }

  myAmapFun.getTransitRoute({
    ...requestParams,
    success: (data) => {
      if (data?.transits) {
        transits.value = data.transits
        mapState.value = false
      } else {
        uni.showToast({ title: '未找到公交路线', icon: 'none' })
      }
    },
    fail: () => uni.showToast({ title: '公交路线获取失败', icon: 'none' })
  })
}

// 公交路线UI相关方法

// 获取预计到达时间
function getArrivalTime(duration) {
  const arrivalTime = new Date(Date.now() + duration * 1000)
  return `${arrivalTime.getHours().toString().padStart(2, '0')}:${arrivalTime.getMinutes().toString().padStart(2, '0')}`
}

// 获取简化的路线段信息
function getSimpleSegments(transit) {
  if (!transit.segments?.length) return []

  const segments = []
  transit.segments.forEach((segment) => {
    // 添加步行段
    if (segment.walking?.distance && parseInt(segment.walking.distance) > 0) {
      segments.push({ type: 'walking', distance: segment.walking.distance })
    }

    // 添加公交段
    if (segment.bus?.buslines?.[0]) {
      const busline = segment.bus.buslines[0]
      const simpleName = busline.name.includes('(') ? busline.name.split('(')[0] : busline.name
      segments.push({ type: 'bus', busline: simpleName, stationCount: busline.via_num || 0 })
    }
  })

  return segments
}

// 切换路线详情展开状态
const toggleDetails = (index) => expandedIndex.value = expandedIndex.value === index ? -1 : index

// 选择公交路线
const selectTransit = (_, index) => uni.showToast({ title: `已选择方案${index + 1}`, icon: 'success', duration: 1500 })


</script>

<style scoped lang="scss">
// 页面布局样式

// 搜索输入区域
.section {
  z-index: 100;
  background: #f8f9fa;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  padding: 16rpx 20rpx;
  gap: 8rpx;
}

// 地图容器
.map_container {
  position: absolute;
  top: 280rpx;
  bottom: 120rpx;
  left: 0;
  right: 0;
  z-index: 10;
}

// 地图组件
.map {
  width: 100%;
  height: 100%;
  border-radius: 16rpx 16rpx 0 0;
}

// 底部信息显示区域
.map_text {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 120rpx;
  background: #fff;
  padding: 0 30rpx;
  z-index: 50;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
}

// 文本样式
text {
  margin: 10rpx 0;
  display: block;
  font-size: 24rpx;
  color: #666;
}

.h1 {
  margin: 0;
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

.text_info_horizontal {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10rpx 0;
  padding: 0 10rpx;
}

.desc_text {
  margin: 0;
  display: block;
  font-size: 24rpx;
  color: #666;
}

// 导航信息文本框
.text_box {
  border-bottom: 2rpx solid #e1e5e9;
  font-size: 26rpx;

  .text {
    display: inline-block;
    margin-right: 30rpx;
    padding: 8rpx 16rpx;
    background: #f0f8ff;
    border-radius: 8rpx;
    color: #0091ff;
    font-weight: 500;
  }
}

// 公交路线容器
.transit_container {
  padding: 20rpx;
  background: #f8f9fa;
  max-height: 100%;
  overflow-y: auto;
  border-radius: 16rpx;

  &::-webkit-scrollbar {
    width: 8rpx;
    &-track { background: #f1f1f1; border-radius: 4rpx; }
    &-thumb {
      background: #c1c1c1;
      border-radius: 4rpx;
      &:hover { background: #a1a1a1; }
    }
  }
}

// 标题区域
.transit-header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e5e5e5;
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.count-text {
  font-size: 24rpx;
  color: #666;
  background: #e8f4fd;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.intercity-tip {
  font-size: 20rpx;
  color: #ff6b35;
  background: #fff3f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #ffccc7;
}

// 路线列表
.transit-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

// 单个路线项
.transit-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
  }
}

// 路线头部
.route-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.time-info {
  display: flex;
  flex-direction: column;
}

.duration {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.2;
}

.arrival {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

.tags {
  display: flex;
  gap: 8rpx;
}

// 标签样式
%tag-base {
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.recommend-tag {
  @extend %tag-base;
  background: #52c41a;
}

.night-tag {
  @extend %tag-base;
  background: #fa8c16;
}

// 路线展示
.route-display {
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.route-line {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
}

.start-point, .end-point {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #1890ff;
  flex-shrink: 0;
  z-index: 3;
  position: relative;
}

.route-segments-container {
  display: flex;
  align-items: center;
  flex: 1;
  overflow-x: auto;
  padding: 0 8rpx;
}

.segment-with-connector {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.route-segment {
  flex-shrink: 0;
  z-index: 2;
  position: relative;
}

.connector-line {
  width: 24rpx;
  height: 3rpx;
  background: #1890ff;
  border-radius: 2rpx;
  flex-shrink: 0;
  margin: 0 2rpx;
  z-index: 1;
}

.walking-segment {
  background: #f8f9fa;
  border: 1rpx solid #dee2e6;
  padding: 8rpx 14rpx;
  border-radius: 16rpx;
  white-space: nowrap;

  .walking-text {
    font-size: 20rpx;
    color: #6c757d;
  }
}

.bus-segment {
  background: #1890ff;
  color: white;
  padding: 10rpx 18rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
  max-width: 160rpx;
  box-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.3);

  .bus-name {
    font-size: 22rpx;
    font-weight: bold;
    margin-bottom: 2rpx;
    text-align: center;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }

  .station-count {
    font-size: 18rpx;
    opacity: 0.9;
  }
}

// 路线信息
.route-info {
  display: flex;
  justify-content: space-around;
  padding: 16rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 16rpx;

  .info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;

    .info-label {
      font-size: 22rpx;
      color: #999;
      margin-bottom: 4rpx;
    }

    .info-value {
      font-size: 24rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

// 展开按钮
.expand-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 0;
  cursor: pointer;

  .expand-text, .expand-icon {
    color: #1890ff;
  }

  .expand-text { font-size: 24rpx; }
  .expand-icon { font-size: 20rpx; }
}

// 详细信息
.route-details {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
  margin-top: 16rpx;

  .detail-item {
    margin-bottom: 20rpx;
    &:last-child { margin-bottom: 0; }
  }
}

// 详情样式 - 通用
%detail-base {
  .detail-header {
    display: flex;
    align-items: center;
    gap: 8rpx;
    margin-bottom: 12rpx;

    .detail-icon { font-size: 24rpx; }
    .detail-title { font-size: 26rpx; font-weight: 500; color: #333; flex: 1; }
    .detail-time { font-size: 22rpx; color: #666; }
  }
}

// 步行详情
.walking-detail {
  @extend %detail-base;

  .walking-steps {
    padding-left: 32rpx;

    .step-text {
      display: block;
      font-size: 22rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 8rpx;
    }
  }
}

// 公交详情
.bus-detail {
  @extend %detail-base;

  .detail-header { margin-bottom: 16rpx; }

  .bus-stations {
    padding-left: 32rpx;

    .station-row {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;

      .station-label {
        font-size: 22rpx;
        color: #666;
        width: 80rpx;
        flex-shrink: 0;
      }

      .station-name {
        font-size: 22rpx;
        color: #333;
        flex: 1;
      }
    }
  }
}
</style>